
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom'

import Header from './components/Header'
import Footer from './components/Footer'
import ChatBot from './components/ChatBot'
import SearchShortcut from './components/SearchShortcut'
import CookieNotification from './components/CookieNotification'
import SearchResultsPage from './pages/SearchResultsPage'
import { SearchProvider } from './contexts/SearchContext'
import HomePage from './pages/HomePage'
import IoTPage from './pages/IoTPage'
import AnalyticsPage from './pages/AnalyticsPage'
import AIPage from './pages/AIPage'
import ContactPage from './pages/ContactPage'
import WebDevPage from './pages/WebDevPage'
import TestingPage from './pages/TestingPage'
import EmbeddedPage from './pages/EmbeddedPage'
import TeamPage from './pages/TeamPage'
import TestimonialsPage from './pages/TestimonialsPage'
import AboutUsPage from './pages/AboutUsPage'
import PhysicalDesignPage from './pages/PhysicalDesignPage'
import PhysicalVerificationPage from './pages/PhysicalVerificationPage'
import Careers from './pages/careers'
import ExecutiveSearchPage from './pages/ExecutiveSearchPage'
import EnterpriseRPOPage from './pages/EnterpriseRPOPage'
import StaffAugmentationPage from './pages/StaffAugmentationPage'
import ContractStaffingPage from './pages/ContractStaffingPage'
import HireTrainDeployPage from './pages/HireTrainDeployPage'
import HireTrainPlacePage from './pages/HireTrainPlacePage'
import ATSDemo from './pages/ATSDemo'
import TradingIntelligence from './pages/TradingIntelligence'
import MakoPlus from './pages/MakoPlus'
import IntegrationPage from './pages/IntegrationPage'


// Wrapper to access route-aware features like useLocation
function AppContent() {
  const location = useLocation()


  const showChatBot = location.pathname !== '/contact'

  return (
    <>
      <SearchShortcut />
      <Header />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/iot" element={<IoTPage />} />
        <Route path="/analytics" element={<AnalyticsPage />} />
        <Route path="/webdev" element={<WebDevPage />} />
        <Route path="/services/testing" element={<TestingPage />} />
        <Route path="/embedded" element={<EmbeddedPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/team" element={<TeamPage />} />
        <Route path="/about-us" element={<AboutUsPage />} />
        <Route path="/testimonials" element={<TestimonialsPage />} />
        <Route path="/physical-design" element={<PhysicalDesignPage />} />
        <Route path="/physical-verification" element={<PhysicalVerificationPage />} />
        <Route path="/services/executive-search" element={<ExecutiveSearchPage />} />
        <Route path="/services/enterprise-rpo" element={<EnterpriseRPOPage />} />
        <Route path="/services/staff-augmentation-new" element={<StaffAugmentationPage />} />
        <Route path="/services/contract-staffing" element={<ContractStaffingPage />} />
        <Route path="/services/hire-train-deploy" element={<HireTrainDeployPage />} />
        <Route path="/services/staff-augmentation-detail" element={<StaffAugmentationPage />} />
        <Route path="/services/integration" element={<WebDevPage />} />
        <Route path="/services/web-development" element={<WebDevPage />} />
        <Route path="/products/ai" element={<AIPage />} />
        <Route path="/semiconductors/physical-verification" element={<PhysicalVerificationPage />} />
        <Route path="/semiconductors/physical-design" element={<PhysicalDesignPage />} />
        <Route path="/Leadership" element={<TeamPage />} />
        <Route path="/careers" element={<Careers />} />
        <Route path="/ats-demo" element={<ATSDemo />} />
        <Route path="/trading-intelligence" element={<TradingIntelligence />} />
        <Route path="/search" element={<SearchResultsPage />} />
        <Route path="/mako-plus" element={<MakoPlus />} />
        <Route path="/integration" element={<IntegrationPage />} />


        {/* <Route path="/careers" element={<Careers />} /> */}
      </Routes>
      <Footer />
      {showChatBot && <ChatBot />}
      <CookieNotification />
    </>
  )
}

function App() {
  return (
    <Router>
      <SearchProvider>
        <AppContent />
      </SearchProvider>
    </Router>
  )
}

export default App

import React, { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, <PERSON>, Col, <PERSON>, But<PERSON>, Badge } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

// Define the page data structure
const webDevPageData = {
  hero: {
    title: "Web & Mobile Development",
    subtitle:
      "We combine in-depth industry expertise with world-class technical knowledge to help you create compelling software-based products that deliver exceptional user experiences.",
    backgroundImage:
      "https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  },
  intro: {
    title: "Transforming Ideas into Digital Reality",
    description:
      "We have a plethora of web and mobile application services with enriched UI/UX that caters to all your business needs. Our esteemed programmers are always dedicated to delivering custom web application development that drives results.",
    image:
      "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  },
  services: [
    {
      id: "web-design",
      title: "Web Design",
      icon: "fa-palette",
      description:
        "Web 3.0 is designed to be a 'smarter web' resulting from the amalgamation of content, social, business, and community. We create interactive, shareable, and connected websites that help prospective clients find relevant information.",
      features: [
        "User Experience & User Interface Design (UI/UX)",
        "High quality responsive designs",
        "Multi-device friendly layouts",
        "Fast loading and SEO friendly",
      ],
      image:
        "https://images.unsplash.com/photo-1559028012-481c04fa702d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1336&q=80",
      color: "#ffffff",
    },
    {
      id: "ecommerce",
      title: "E-commerce",
      icon: "fa-shopping-cart",
      description:
        "There are many factors that make an e-commerce site user-friendly, from information loading to fast checkout processes. Everything should be dynamic, responsive, and fast. We aim for robust, easy-to-handle websites with all the features you need.",
      features: [
        "Secure payment gateways",
        "Inventory management",
        "Mobile-optimized shopping experience",
        "Customer account management",
        // "Order tracking and history",
        // "Analytics and reporting",
      ],
      image:
        "https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#ffffff",
    },
    {
      id: "web-app",
      title: "Web Applications",
      icon: "fa-laptop-code",
      description:
        "Web-based applications communicate with users via HTTP and can range from light applications like calculators and calendars to intensive applications like word processors and spreadsheets. We develop robust, scalable web applications tailored to your business needs.",
      features: [
        "Progressive Web Apps (PWAs)",
        "Single Page Applications (SPAs)",
        "Custom business applications",
        "Cloud-based solutions",
        // "API development and integration",
        // "Real-time data processing",
      ],
      image:
        "https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#ffffff",
    },
    {
      id: "digital-marketing",
      title: "Digital Marketing",
      icon: "fa-bullhorn",
      description:
        "Digital marketing is essential in today's fast-paced digital world. From online advertising to social media management, we ensure your digital presence is optimized for maximum impact and engagement with your target audience.",
      features: [
        "Search Engine Optimization (SEO)",
        "Social Media Marketing",
        "Content Marketing",
        "Email Marketing Campaigns",
        // "Pay-Per-Click (PPC) Advertising",
        // "Analytics and Performance Tracking",
      ],
      image:
        "https://images.unsplash.com/photo-1533750349088-cd871a92f312?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#ffffff",
    },
  ],
  technologies: {
    title: "Technologies We Use",
    description:
      "We leverage the latest technologies to build robust, scalable, and future-proof applications.",
    techs: [
      { name: "React", icon: "fab fa-react" },
      { name: "Angular", icon: "fab fa-angular" },
      { name: "Vue.js", icon: "fab fa-vuejs" },
      { name: "Node.js", icon: "fab fa-node-js" },
      { name: "PHP", icon: "fab fa-php" },
      { name: "WordPress", icon: "fab fa-wordpress" },
      { name: "HTML5", icon: "fab fa-html5" },
      { name: "CSS3", icon: "fab fa-css3-alt" },
      { name: "JavaScript", icon: "fab fa-js" },
      { name: "Python", icon: "fab fa-python" },
      { name: "Swift", icon: "fab fa-swift" },
      { name: "Android", icon: "fab fa-android" },
    ],
  },
};

const WebDevPage = () => {
  const [isCtaButtonHovered, setIsCtaButtonHovered] = useState(false);
  const [activeService, setActiveService] = useState("web-design");
  const [animatedElements, setAnimatedElements] = useState({});
  const elementsRef = useRef({});
  const heroRef = useRef(null);
  const ctaRef = useRef(null);

  // Style constants - Updated to match Makonis brand colors
  const primaryColor = "#002956";
  const secondaryColor = "#00a0e9";
  const primaryRgb = "0, 41, 86";
  const secondaryRgb = "0, 160, 233";

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animations
      if (heroRef.current) {
        gsap.from(heroRef.current.querySelector("h1"), {
          y: 100,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
        });

        gsap.from(heroRef.current.querySelector("p"), {
          y: 50,
          opacity: 0,
          duration: 1,
          ease: "power2.out",
          delay: 0.3,
        });

        gsap.from(heroRef.current.querySelectorAll(".btn"), {
          y: 30,
          opacity: 0,
          duration: 0.8,
          ease: "back.out(1.7)",
          delay: 0.6,
        });
      }

      // CTA section animation
      if (ctaRef.current) {
        gsap.from(ctaRef.current.querySelector("h2"), {
          y: 50,
          opacity: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: ctaRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        });
      }
    });

    return () => ctx.revert();
  }, []);

  // Button styles - Updated to match Makonis design
  const ctaButtonBaseStyle = {
    padding: "1.2rem 3rem",
    fontSize: "1.2rem",
    background: `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`,
    border: "none",
    borderRadius: "50px",
    boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
    transition: "all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)",
    transform: "translateY(0)",
    color: "#fff",
    textDecoration: "none",
    display: "inline-block",
  };

  const ctaButtonHoverStyle = {
    background: `linear-gradient(135deg, ${secondaryColor} 0%, ${primaryColor} 100%)`,
    boxShadow: `0 15px 30px rgba(${secondaryRgb}, 0.4)`,
    transform: "translateY(-3px) scale(1.02)",
  };

  // Set up intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setAnimatedElements((prev) => ({
              ...prev,
              [entry.target.id]: true,
            }));
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe all elements with refs
    Object.keys(elementsRef.current).forEach((key) => {
      if (elementsRef.current[key]) {
        observer.observe(elementsRef.current[key]);
      }
    });

    return () => {
      Object.keys(elementsRef.current).forEach((key) => {
        if (elementsRef.current[key]) {
          observer.unobserve(elementsRef.current[key]);
        }
      });
    };
  }, []);

  // Helper function to create refs
  const createRef = (id) => {
    if (!elementsRef.current[id]) {
      elementsRef.current[id] = React.createRef();
    }
    return elementsRef.current[id];
  };

  return (
    <div
      className="webdev-page overflow-hidden"
      style={{
        background:
          "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="webdev-hero d-flex align-items-center text-white text-center position-relative overflow-hidden"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          minHeight: "100vh",
          position: "relative",
        }}
      >
        {/* Grid Pattern Overlay */}
        <div
          className="position-absolute w-100 h-100"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
            opacity: 0.2,
            pointerEvents: "none",
          }}
        />

        {/* Floating Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="position-absolute rounded-circle"
              style={{
                width: `${Math.random() * 100 + 50}px`,
                height: `${Math.random() * 100 + 50}px`,
                background: `rgba(0, 160, 233, ${Math.random() * 0.1 + 0.05})`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float ${
                  Math.random() * 10 + 10
                }s infinite ease-in-out`,
                backdropFilter: "blur(1px)",
              }}
            />
          ))}
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <div className="row align-items-center">
            <h1
              className="display-1 fw-bold mb-4"
              style={{
                fontSize: "clamp(3rem, 8vw, 4.4rem)",
                lineHeight: "1.1",
                background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              {webDevPageData.hero.title}
            </h1>
            <div className="col-lg-6 text-white">
              <p
                className="text-white/80 mb-5"
                style={{ fontSize: "1.45rem", lineHeight: "1.6" }}
              >
                {webDevPageData.hero.subtitle}
              </p>
              <div className="d-flex flex-wrap gap-3 align-items-center"></div>
            </div>
            <div className="col-lg-6">
              <div className="position-relative">
                <div
                  className="card-makonis-glass p-4"
                  style={{
                    background: "rgba(255, 255, 255, 0.08)",
                    backdropFilter: "blur(20px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                  }}
                >
                  <img
                    src={webDevPageData.hero.backgroundImage}
                    alt="Web Development"
                    className="w-100 rounded-3"
                    style={{ height: "350px", objectFit: "cover" }}
                  />
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Intro Section */}
      <section
        className="section-padding"
        id="intro-section"
        ref={(el) => (elementsRef.current["intro-section"] = el)}
      >
        <Container className="container-makonis">
          <Row className="align-items-center g-5">
            <Col
              lg={6}
              className={`mb-4 mb-lg-0 ${
                animatedElements["intro-section"]
                  ? "animate__animated animate__fadeInLeft"
                  : ""
              }`}
            >
              <div className="position-relative"></div>
              <h2
                style={{
                  fontSize: "3rem",
                  fontWeight: "800",
                  letterSpacing: "2.6px",
                  marginBottom: "1rem",
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                }}
              >
                {webDevPageData.intro.title}
              </h2>
              <p
                className="lead mb-4 text-white"
                style={{ fontSize: "1.3rem", lineHeight: "1.8" }}
              >
                {webDevPageData.intro.description}
              </p>
              <div className="d-flex flex-wrap gap-3 mt-4"></div>
            </Col>
            <Col
              lg={6}
              className={`${
                animatedElements["intro-section"]
                  ? "animate__animated animate__fadeInRight"
                  : ""
              }`}
            >
              <div
                className="card-makonis-glass position-relative overflow-hidden"
                style={{ height: "400px" }}
              >
                <img
                  src={webDevPageData.intro.image}
                  alt="Web Development Team"
                  className="w-100 h-100"
                  style={{
                    objectFit: "cover",
                    transition: "transform 0.6s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = "scale(1.05)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = "scale(1)";
                  }}
                />
                <div
                  className="position-absolute inset-0"
                  style={{
                    background: `linear-gradient(135deg, rgba(${primaryRgb}, 0.1) 0%, transparent 50%, rgba(${secondaryRgb}, 0.1) 100%)`,
                  }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <section
        className="section-padding"
        id="services-section"
        ref={(el) => (elementsRef.current["services-section"] = el)}
        style={{ background: "rgba(255, 255, 255, 0.02)" }}
      >
        <Container className="container-makonis">
          <div className="text-center mb-2">
            <h2
              style={{
                fontSize: "3.6rem",
                fontWeight: "800",
                letterSpacing: "2.6px",
                marginBottom: "1rem",
                background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              Comprehensive Web Solutions
            </h2>
            <p
              className="lead mx-auto text-white"
              style={{ maxWidth: "1200px", opacity: 0.9 }}
            >
              We offer a wide range of professional web and mobile development
              services, designed to meet the unique needs of your business. Our
              solutions are scalable, user-friendly, and tailored to help you
              grow and succeed in the digital world.
            </p>
          </div>

          {/* Service Tabs */}
          <div className="service-tabs mb-5">
            <div className="d-flex justify-content-center flex-wrap">
              {webDevPageData.services.map((service) => (
                <button
                  key={service.id}
                  className={`btn m-2 rounded-pill px-4 py-3 fw-semibold transition-all duration-300 ${
                    activeService === service.id
                      ? "btn-makonis-primary"
                      : "btn-makonis-ghost"
                  }`}
                  onClick={() => setActiveService(service.id)}
                  style={{
                    border:
                      activeService === service.id
                        ? "none"
                        : `1px solid rgba(${secondaryRgb}, 0.3)`,
                    boxShadow:
                      activeService === service.id
                        ? `0 8px 25px rgba(${primaryRgb}, 0.3)`
                        : "none",
                  }}
                >
                  <i className={`fas ${service.icon} me-2`}></i>
                  {service.title}
                </button>
              ))}
            </div>
          </div>

          {/* Active Service Content */}
          {webDevPageData.services.map((service) => (
            <div
              key={service.id}
              className={`service-content ${
                activeService === service.id ? "d-block" : "d-none"
              }`}
            >
              <Row className="align-items-center g-5">
                <Col lg={6} className="order-lg-2">
                  <div
                    className="card-makonis-glass position-relative overflow-hidden"
                    style={{ height: "400px" }}
                  >
                    <div
                      className="position-absolute inset-0"
                      style={{
                        background: `linear-gradient(135deg, ${service.color}40 0%, transparent 50%, rgba(${secondaryRgb}, 0.1) 100%)`,
                        zIndex: 1,
                      }}
                    />
                    <img
                      src={service.image}
                      alt={service.title}
                      className="w-100 h-100"
                      style={{
                        objectFit: "cover",
                        transition: "transform 0.6s ease",
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.transform = "scale(1.05)";
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.transform = "scale(1)";
                      }}
                    />
                  </div>
                </Col>
                <Col lg={6} className="order-lg-1">
                  <h3
                    className="display-5 fw-bold mb-4"
                    style={{
                      background: `linear-gradient(135deg, ${service.color} 0%, ${secondaryColor} 100%)`,
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                    }}
                  >
                    {service.title}
                  </h3>
                  <p className="lead mb-4 text-white" style={{ opacity: 0.9 }}>
                    {service.description}
                  </p>
                  <div className="features-list">
                    {service.features.map((feature, index) => (
                      <div
                        key={index}
                        className="feature-item d-flex align-items-start mb-3"
                        style={{ transition: "all 0.3s ease" }}
                      >
                        <div
                          className="feature-icon me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: `rgba(${secondaryRgb}, 0.2)`,
                            color: secondaryColor,
                            flexShrink: 0,
                            border: `1px solid rgba(${secondaryRgb}, 0.3)`,
                          }}
                        >
                          <i className="fas fa-check"></i>
                        </div>
                        <div>
                          <p
                            className="mb-0 fw-medium text-white"
                            style={{ opacity: 0.9 }}
                          >
                            {feature}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </Col>
              </Row>
            </div>
          ))}
        </Container>
      </section>

      {/* Technologies Section */}
      <section
        className="section-padding"
        id="tech-section"
        ref={(el) => (elementsRef.current["tech-section"] = el)}
      >
        <Container className="container-makonis">
          <div className="text-center mb-5">
            <h2
              style={{
                fontSize: "3.6rem",
                fontWeight: "800",
                letterSpacing: "2.6px",
                marginBottom: "1rem",
                background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              {webDevPageData.technologies.title}
            </h2>
            <p
              className="lead mx-auto text-white"
              style={{ maxWidth: "1000px", opacity: 0.9 }}
            >
              {webDevPageData.technologies.description}
            </p>
          </div>

          <Row className="justify-content-center">
            <Col lg={10}>
              <div className="tech-grid">
                <Row className="g-4">
                  {webDevPageData.technologies.techs.map((tech, index) => (
                    <Col xs={6} sm={4} md={3} key={index}>
                      <div
                        className={`card-makonis tech-item text-center p-4 h-100 ${
                          animatedElements["tech-section"]
                            ? "animate__animated animate__fadeIn"
                            : ""
                        }`}
                        style={{
                          animationDelay: `${index * 0.1}s`,
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(10px)",
                          border: `1px solid rgba(${secondaryRgb}, 0.2)`,
                          transition: "all 0.3s ease",
                          transform: "translateY(0)",
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform =
                            "translateY(-10px) scale(1.02)";
                          e.currentTarget.style.boxShadow = `0 20px 40px rgba(${secondaryRgb}, 0.2)`;
                          e.currentTarget.style.borderColor = secondaryColor;
                          e.currentTarget.style.background =
                            "rgba(255, 255, 255, 0.08)";
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform =
                            "translateY(0) scale(1)";
                          e.currentTarget.style.boxShadow =
                            "0 10px 25px rgba(0, 41, 86, 0.15)";
                          e.currentTarget.style.borderColor = `rgba(${secondaryRgb}, 0.2)`;
                          e.currentTarget.style.background =
                            "rgba(255, 255, 255, 0.05)";
                        }}
                      >
                        <div className="tech-icon mb-3">
                          <i
                            className={`${tech.icon} fa-3x`}
                            style={{ color: secondaryColor }}
                          ></i>
                        </div>
                        <h5 className="tech-name text-white fw-semibold">
                          {tech.name}
                        </h5>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Global CSS for animations */}
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(10deg);
          }
        }

        .transition-all {
          transition: all 0.3s ease;
        }

        .badge {
          font-weight: 600;
          letter-spacing: 0.5px;
        }

        .card-makonis-glass:hover {
          transform: translateY(-5px);
          box-shadow: 0 25px 50px rgba(0, 160, 233, 0.2);
        }

        .btn-makonis-primary:hover {
          transform: translateY(-2px) scale(1.02);
        }

        .btn-makonis-ghost:hover {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(0, 160, 233, 0.5);
        }
      `}</style>
    </div>
  );
};

export default WebDevPage;

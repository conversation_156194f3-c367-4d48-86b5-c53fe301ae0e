import React, { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// It's good practice to register plugins at the top level
gsap.registerPlugin(ScrollTrigger);

const IntegrationPage = () => {
  // --- DATA --- //

  const engagementModels = [
    { title: "Staff Augmentation" },
    { title: "Managed T&M" },
    { title: "Fixed Bid" },
    { title: "Managed Solutions" },
    { title: "COE/ODC" }
  ];

  const benefits = [
    "Faster Time to Market",
    "Global Teams for Cost Efficiency & Enhanced Business Value",
    "Minimized Business & Operational Risks",
    "Improved Business Agility",
    "Reduced Total Cost of Ownership",
    "Efficient & Adaptive Resource Management"
  ];
  
  // ADDED more data: icons and descriptions for each competency
  const competencies = [
    { 
      name: "Full Stack Development",
      icon: "fas fa-code",
      description: "End-to-end web solutions from front-end interfaces to back-end infrastructure." 
    },
    { 
      name: "Data Engineering",
      icon: "fas fa-database",
      description: "Building robust, scalable pipelines to process and manage your data assets." 
    },
    { 
      name: "DevOps & SRE",
      icon: "fas fa-sync-alt",
      description: "Automating workflows and ensuring system reliability for high-performance operations." 
    },
    { 
      name: "Cloud Architecture",
      icon: "fas fa-cloud",
      description: "Designing and managing secure, scalable, and cost-efficient cloud environments." 
    },
    { 
      name: "AI/ML Solutions",
      icon: "fas fa-robot",
      description: "Leveraging artificial intelligence to unlock insights and automate complex processes." 
    },
    { 
      name: "System Integration",
      icon: "fas fa-project-diagram",
      description: "Connecting disparate systems and applications into one cohesive, efficient ecosystem." 
    },
    { 
      name: "Database Management",
      icon: "fas fa-server",
      description: "Optimizing database performance, security, and scalability for your applications." 
    },
    { 
      name: "Agile Project Management",
      icon: "fas fa-tasks",
      description: "Employing agile methodologies to deliver projects with speed, flexibility, and precision." 
    }
  ];

  // ADDED more data: A new section with key advantages
  const advantages = [
    {
      icon: "fas fa-users-cog",
      title: "Expert-Led Teams",
      description: "Our teams are comprised of certified professionals and subject matter experts dedicated to your project's success."
    },
    {
      icon: "fas fa-lightbulb",
      title: "Innovative Solutions",
      description: "We leverage the latest technologies and forward-thinking strategies to build solutions that are future-ready."
    },
    {
      icon: "fas fa-shield-alt",
      title: "Proven & Secure Process",
      description: "With a time-tested and security-first approach, we ensure reliable and transparent delivery from start to finish."
    }
  ];


  // --- ANIMATIONS --- //

  useEffect(() => {
    // Hero Section Animation
    gsap.fromTo(".hero-content > *", { opacity: 0, x: -50 }, { opacity: 1, x: 0, duration: 1, stagger: 0.2, ease: "power3.out", delay: 0.5 });
    gsap.fromTo(".hero-image", { opacity: 0, scale: 0.9 }, { opacity: 1, scale: 1, duration: 1.2, ease: "power3.out", delay: 0.3 });

    // Animate Sections on Scroll
    gsap.utils.toArray('.animated-section').forEach(section => {
      gsap.fromTo(section, { opacity: 0, y: 100 }, {
        opacity: 1, y: 0, duration: 1, ease: "power3.out",
        scrollTrigger: { trigger: section, start: "top 85%", toggleActions: "play none none none" }
      });
    });
    
    // "Step-Up" Models Animation
    if (document.querySelector(".step-item")) {
      gsap.fromTo(".step-item", { opacity: 0, y: 40, x: -40 }, {
        opacity: 1, y: 0, x: 0, duration: 0.8, ease: 'power3.out', stagger: 0.2,
        scrollTrigger: { trigger: '.steps-container', start: 'top 70%' }
      });
    }

    // Competency Grid Animation
    if (document.querySelector(".competency-card")) {
      gsap.fromTo(".competency-card", { opacity: 0, y: 50, scale: 0.9 }, {
        opacity: 1, y: 0, scale: 1, duration: 0.5, ease: 'power3.out', stagger: 0.1,
        scrollTrigger: { trigger: '.competencies-grid', start: 'top 80%' }
      });
    }

  }, []);

  // --- COMPONENT RENDER --- //

  return (
    <div style={{ background: 'linear-gradient(135deg, #002956 0%, #001a3d 50%, #000d1a 100%)' }}>
      
      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center py-20 px-4">
        <div className="container mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div className="hero-content text-white">
            <h1 className="text-5xl md:text-6xl font-bold leading-tight" style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
              WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text' , paddingBottom: '10px'
            }}>
              Seamless Integration, Evolved Engagement
            </h1>
            <p className="text-xl text-gray-300 mt-6 leading-relaxed">
              We provide end-to-end integration services powered by flexible engagement models to accelerate your digital transformation.
            </p>
          </div>
          <div className="hero-image flex justify-center items-center">
             <img src="https://images.pexels.com/photos/7688460/pexels-photo-7688460.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Abstract network of digital connections" className="rounded-2xl shadow-2xl w-full h-auto object-cover max-h-[500px]" />
          </div>
        </div>
      </section>

      {/* Evolution of Engagement Models Section */}
      <section className="py-24 animated-section">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold" style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
              WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text'
            }}>Evolution of Engagement Models</h2>
            <p className="text-lg text-gray-400 mt-4 max-w-3xl mx-auto">We adapt to your needs, offering a spectrum of models from simple augmentation to full-scale development centers.</p>
          </div>
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left side: Visual "Step-Up" representation */}
            <div className="steps-container relative flex items-end justify-start h-[450px]">
              {engagementModels.map((model, index) => (
                <div key={index} className="step-item absolute" style={{ bottom: `${index * 20}%`, left: `${index * 15}%` }}>
                  {index > 0 && (
                    <div className="absolute w-16 h-16 -top-8 -left-8">
                      <svg viewBox="0 0 100 100" className="w-full h-full">
                        <path d="M 0,100 Q 50,100 50,50 Q 50,0 100,0" stroke="#00a0e9" strokeDasharray="4 4" strokeWidth="2" fill="none" />
                      </svg>
                    </div>
                  )}
                  <div className="relative w-48 p-4 rounded-lg bg-[#00a0e9]/10 border border-[#00a0e9]/30 backdrop-blur-sm shadow-md text-center transform hover:scale-105 hover:border-[#00a0e9] transition-all duration-300">
                    <h3 className="text-lg font-semibold text-white">{model.title}</h3>
                  </div>
                </div>
              ))}
            </div>
            {/* Right side: Benefits */}
            <div className="p-8 rounded-2xl bg-gray-500/10 border border-gray-400/20 backdrop-blur-lg">
              <h3 className="text-2xl font-bold text-white mb-6">Delivering Tangible Business Value</h3>
              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-6 h-6 rounded-full flex items-center justify-center bg-[#00A0E9]/30 text-[#00A0E9] mr-4 mt-1 flex-shrink-0">
                      <i className="fas fa-check text-xs"></i>
                    </div>
                    <span className="text-gray-300 leading-relaxed">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* NEW SECTION: The Makonis Advantage */}
      <section className="py-24 animated-section">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold" style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
              WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text'
            }}>The Makonis Advantage</h2>
            <p className="text-lg text-gray-400 mt-4 max-w-3xl mx-auto">We combine technology, talent, and process to deliver unparalleled results and a seamless client experience.</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {advantages.map((advantage, index) => (
              <div key={index} className="advantage-card text-center p-8 rounded-xl bg-gray-500/10 border border-gray-400/20 backdrop-blur-lg transform hover:-translate-y-2 transition-transform duration-300">
                <div className="text-4xl text-[#ff6b35] mb-4">
                  <i className={advantage.icon}></i>
                </div>
                <h3 className="text-2xl font-semibold text-white mb-3">{advantage.title}</h3>
                <p className="text-gray-400 leading-relaxed">{advantage.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* REDESIGNED: Our Core Competencies Section */}
      <section className="py-24 animated-section">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-2" style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
              WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text'
            }}>Our Core Competencies</h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto mb-16">A comprehensive suite of technical capabilities to architect, build, and scale modern enterprise solutions.</p>
          <div className="competencies-grid grid grid-cols-2 md:grid-cols-4 gap-8">
            {competencies.map((comp, index) => (
              <div key={index} className="competency-card p-6 bg-[#00a0e9]/5 rounded-xl border border-transparent hover:border-[#00a0e9]/50 hover:bg-[#00a0e9]/10 transition-all duration-300">
                <div className="text-4xl text-[#00a0e9] mb-5">
                  <i className={comp.icon}></i>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{comp.name}</h3>
                <p className="text-sm text-gray-400 leading-snug">{comp.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

    </div>
  );
};

export default IntegrationPage;
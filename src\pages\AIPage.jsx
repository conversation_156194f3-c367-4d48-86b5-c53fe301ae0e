import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Tabs, Tab } from 'react-bootstrap';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";

gsap.registerPlugin(ScrollTrigger);

// --- DATA FOR THE PAGE (NO CHANGES) ---
const aiPageData = {
  hero: {
    title: "Intelligent Solutions for Tomorrow",
    subtitle: "Harness the power of Artificial Intelligence to transform your business operations, automate complex processes, and unlock unprecedented insights from your data.",
    backgroundImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  solutions: {
    title: "Our AI Solutions",
    description: "We offer a comprehensive suite of AI services. Explore our capabilities to see how we can drive your business forward.",
    items: [
      {
        eventKey: "ml",
        title: "Machine Learning",
        text: "Our Machine Learning solutions focus on creating systems that learn and adapt from data. We build custom models to automate processes, predict outcomes, and provide actionable insights that give you a competitive edge.",
        services: ["Predictive Analytics & Forecasting", "Recommendation Engines", "Custom Algorithm Development", "Data Clustering & Classification"],
        image: "https://images.unsplash.com/photo-1633409361619-b7b2524a3c40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
      },
      {
        eventKey: "cv",
        title: "Computer Vision",
        text: "We empower machines to see and interpret the world. Our computer vision services automate tasks that typically require human vision, such as quality control, surveillance, and medical image analysis, with superhuman accuracy and speed.",
        services: ["Object Detection & Tracking", "Facial Recognition Systems", "Image & Video Analysis", "Automated Quality Inspection"],
        image: "https://images.unsplash.com/photo-1579551065094-13a83a04c219?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
      },
      {
        eventKey: "nlp",
        title: "Natural Language Processing",
        text: "Unlock the value of unstructured text data. Our NLP solutions enable machines to understand and process human language, powering everything from intelligent chatbots and virtual assistants to sentiment analysis and document summarization.",
        services: ["Sentiment Analysis", "Intelligent Chatbots", "Text Summarization", "Document Processing & Extraction"],
        image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80"
      }
    ]
  },
  process: {
    title: "Our Development Process",
    description: "We follow a structured, agile methodology to ensure project success and deliver measurable results.",
    steps: [
      {
        title: "Discovery & Consultation",
        text: "We start by understanding your goals and challenges to define a clear AI strategy and project roadmap."
      },
      {
        title: "Data Strategy & Preparation",
        text: "Our team collects, cleans, and processes your data to create high-quality datasets ready for model training."
      },
      {
        title: "Model Development",
        text: "Using the prepared data, we build and train custom AI models tailored to your specific requirements."
      },
      {
        title: "Deployment & Integration",
        text: "We seamlessly deploy the AI model into your existing systems and workflows with minimal disruption."
      },
      {
        title: "Monitoring & Optimization",
        text: "Post-deployment, we continuously monitor performance and retrain the model to ensure long-term accuracy and value."
      }
    ]
  },
  industries: {
    title: "Industries We Serve",
    description: "Our AI solutions are versatile and can be adapted to drive innovation across a wide range of industries.",
    items: [
      { icon: "fas fa-heartbeat", name: "Healthcare" },
      { icon: "fas fa-dollar-sign", name: "Finance & Banking" },
      { icon: "fas fa-shopping-cart", name: "E-commerce & Retail" },
      { icon: "fas fa-industry", name: "Manufacturing" }
    ]
  },
  faq: {
    title: "Frequently Asked Questions",
    items: [
      {
        question: "What kind of data do I need for an AI project?",
        answer: "The data required depends on the problem you're trying to solve. Typically, you need a substantial amount of high-quality, relevant data. We can help you with data strategy and acquisition if you don't have the necessary data on hand."
      },
      {
        question: "How long does a typical AI project take?",
        answer: "Project timelines vary based on complexity, data availability, and the scope of work. A pilot project can take a few weeks, while a full-scale enterprise solution may take several months. We provide a detailed timeline after the initial discovery phase."
      },
      {
        question: "How do you ensure the AI model is accurate?",
        answer: "We use rigorous testing and validation techniques on historical data. After deployment, we continuously monitor the model's performance against live data and retrain it as needed to maintain high accuracy and adapt to new patterns."
      }
    ]
  },
  cta: {
    title: "Ready to Build the Future?",
    text: "Let's discuss how our AI expertise can be tailored to meet your unique business goals.",
    buttonText: "Schedule a Consultation",
    buttonLink: "/contact"
  }
};


// --- THE REACT COMPONENT ---
const AIPage = () => {
  const [key, setKey] = useState('ml');
  const heroRef = useRef(null);

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animations
      gsap.from(heroRef.current.querySelector('h1'), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out"
      });

      gsap.from(heroRef.current.querySelector('p'), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Reusable Style Objects
  const primaryColor = "#007bff";
  const primaryDarkColor = "#0056b3";
  const primaryRgb = "0,123,255";

  return (
    <div className="ai-page-wrapper">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="ai-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${aiPageData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating AI Icons */}
          {[
            { icon: "fa-brain", top: "15%", left: "10%", delay: 0 },
            { icon: "fa-robot", top: "25%", right: "15%", delay: 1 },
            { icon: "fa-microchip", bottom: "20%", left: "8%", delay: 2 },
            { icon: "fa-network-wired", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  fontSize: "24px",
                  color: "#00a0e9",
                  textShadow: "0 0 10px rgba(0, 160, 233, 0.5)",
                }}
              ></i>
            </div>
          ))}

          {/* Pulse Circles */}
          {[
            { size: "200px", top: "10%", right: "20%", delay: "0s" },
            { size: "150px", bottom: "15%", left: "15%", delay: "2s" },
            { size: "100px", top: "60%", right: "10%", delay: "4s" },
          ].map((circle, index) => (
            <div
              key={index}
              className="position-absolute rounded-circle"
              style={{
                width: circle.size,
                height: circle.size,
                top: circle.top,
                bottom: circle.bottom,
                left: circle.left,
                right: circle.right,
                background: "rgba(0, 160, 233, 0.05)",
                border: "1px solid rgba(0, 160, 233, 0.1)",
                animation: `pulse 4s ease-in-out infinite`,
                animationDelay: circle.delay,
              }}
            ></div>
          ))}
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <h1
            className="display-1 fw-bolder mb-4 animate__animated animate__fadeInDown animate__slow"
            style={{
              fontSize: "3.6rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            {aiPageData.hero.title}
          </h1>
          <p
            className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "1.35rem",
            }}
          >
            {aiPageData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Feature Sections */}
      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {aiPageData.solutions.items.map((solution, idx) => (
            <section
              key={solution.eventKey}
              className="mb-5 mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row className="mb-4">
                <Col xs={12}>
                  <h2
                    className="display-5 fw-bold text-center"
                    style={{
                      fontSize: "3.6rem",
                      fontWeight: "800",
                      letterSpacing: "2.6px",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {solution.title}
                  </h2>
                </Col>
              </Row>

              {/* Enhanced Accent Line */}
              <div className="w-30 h-1 mx-auto relative mb-5">
                <div
                  className="w-full h-full rounded-sm shadow-glow"
                  style={{
                    background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                  }}
                />
              </div>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  idx % 2 === 1 ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={10}
                  className={`${idx % 2 === 1 ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    <p
                      className="mb-3 mb-md-4"
                      style={{
                        fontSize: "1.3rem",
                        lineHeight: "1.7",
                        color: "rgba(255, 255, 255, 0.9)",
                        textAlign: "justify",
                      }}
                    >
                      {solution.text}
                    </p>

                    <ul
                      className="list-unstyled mt-4"
                      style={{
                        fontSize: "1.2rem",
                        lineHeight: "1.8",
                        color: "rgba(255, 255, 255, 0.9)",
                      }}
                    >
                      {solution.services.map((service, index) => (
                        <li
                          key={index}
                          className="mb-3 d-flex align-items-center"
                          style={{
                            padding: "0.8rem 0",
                          }}
                        >
                          <div
                            className="me-3 d-flex align-items-center justify-content-center"
                            style={{
                              width: "40px",
                              height: "40px",
                              borderRadius: "50%",
                              background: "rgba(0, 160, 233, 0.2)",
                              border: "1px solid rgba(0, 160, 233, 0.3)",
                            }}
                          >
                            <i
                              className="fas fa-check"
                              style={{
                                color: "#00a0e9",
                                fontSize: "16px",
                              }}
                            ></i>
                          </div>
                          {service}
                        </li>
                      ))}
                    </ul>
                  </div>
                </Col>
                <Col lg={6} md={10} className={`${idx % 2 === 1 ? "pe-lg-4" : "ps-lg-4"}`}>
                  <div
                    className="image-container"
                    style={{
                      borderRadius: "1.25rem",
                      overflow: "hidden",
                      boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                      transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                      backgroundColor: "#f0f2f5",
                    }}
                  >
                    <img
                      src={solution.image}
                      alt={solution.title}
                      style={{
                        width: "100%",
                        height: "100%",
                        minHeight: "400px",
                        objectFit: "cover",
                        transition: "transform 0.6s ease",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

      {/* Global CSS */}
      <style>{`
        :root {
          --bs-primary: ${primaryColor};
          --bs-primary-dark: ${primaryDarkColor};
          --bs-primary-rgb: ${primaryRgb};
        }

        h1, h2, h3, h4, h5, h6 {
          line-height: 1.2;
        }

        p {
          line-height: 1.75;
        }

        .container {
          padding-left: 1.5rem;
          padding-right: 1.5rem;
        }

        .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
        .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
        .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

        .content-wrapper {
          padding: 0;
        }

        .image-container {
          position: relative;
          overflow: hidden;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.6;
          }
          33% {
            transform: translateY(-15px) rotate(120deg);
            opacity: 1;
          }
          66% {
            transform: translateY(5px) rotate(240deg);
            opacity: 0.8;
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.15;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.25;
          }
        }

        @media (min-width: 768px) {
          .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
          .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
          .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
          .mb-md-5 { margin-bottom: 4rem !important; }
          .mb-md-6 { margin-bottom: 6rem !important; }
          .mb-md-8 { margin-bottom: 8rem !important; }
        }
      `}</style>
    </div>
  );
};

export default AIPage;
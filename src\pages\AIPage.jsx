import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Con<PERSON><PERSON>, Row, Col, Button, <PERSON>, Tabs, Tab } from 'react-bootstrap';

// --- DATA FOR THE PAGE (NO CHANGES) ---
const aiPageData = {
  hero: {
    title: "Intelligent Solutions for Tomorrow",
    subtitle: "Harness the power of Artificial Intelligence to transform your business operations, automate complex processes, and unlock unprecedented insights from your data.",
    backgroundImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  solutions: {
    title: "Our AI Solutions",
    description: "We offer a comprehensive suite of AI services. Explore our capabilities to see how we can drive your business forward.",
    items: [
      {
        eventKey: "ml",
        title: "Machine Learning",
        text: "Our Machine Learning solutions focus on creating systems that learn and adapt from data. We build custom models to automate processes, predict outcomes, and provide actionable insights that give you a competitive edge.",
        services: ["Predictive Analytics & Forecasting", "Recommendation Engines", "Custom Algorithm Development", "Data Clustering & Classification"],
        image: "https://images.unsplash.com/photo-1633409361619-b7b2524a3c40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
      },
      {
        eventKey: "cv",
        title: "Computer Vision",
        text: "We empower machines to see and interpret the world. Our computer vision services automate tasks that typically require human vision, such as quality control, surveillance, and medical image analysis, with superhuman accuracy and speed.",
        services: ["Object Detection & Tracking", "Facial Recognition Systems", "Image & Video Analysis", "Automated Quality Inspection"],
        image: "https://images.unsplash.com/photo-1579551065094-13a83a04c219?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
      },
      {
        eventKey: "nlp",
        title: "Natural Language Processing",
        text: "Unlock the value of unstructured text data. Our NLP solutions enable machines to understand and process human language, powering everything from intelligent chatbots and virtual assistants to sentiment analysis and document summarization.",
        services: ["Sentiment Analysis", "Intelligent Chatbots", "Text Summarization", "Document Processing & Extraction"],
        image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80"
      }
    ]
  },
  process: {
    title: "Our Development Process",
    description: "We follow a structured, agile methodology to ensure project success and deliver measurable results.",
    steps: [
      {
        title: "Discovery & Consultation",
        text: "We start by understanding your goals and challenges to define a clear AI strategy and project roadmap."
      },
      {
        title: "Data Strategy & Preparation",
        text: "Our team collects, cleans, and processes your data to create high-quality datasets ready for model training."
      },
      {
        title: "Model Development",
        text: "Using the prepared data, we build and train custom AI models tailored to your specific requirements."
      },
      {
        title: "Deployment & Integration",
        text: "We seamlessly deploy the AI model into your existing systems and workflows with minimal disruption."
      },
      {
        title: "Monitoring & Optimization",
        text: "Post-deployment, we continuously monitor performance and retrain the model to ensure long-term accuracy and value."
      }
    ]
  },
  industries: {
    title: "Industries We Serve",
    description: "Our AI solutions are versatile and can be adapted to drive innovation across a wide range of industries.",
    items: [
      { icon: "fas fa-heartbeat", name: "Healthcare" },
      { icon: "fas fa-dollar-sign", name: "Finance & Banking" },
      { icon: "fas fa-shopping-cart", name: "E-commerce & Retail" },
      { icon: "fas fa-industry", name: "Manufacturing" }
    ]
  },
  faq: {
    title: "Frequently Asked Questions",
    items: [
      {
        question: "What kind of data do I need for an AI project?",
        answer: "The data required depends on the problem you're trying to solve. Typically, you need a substantial amount of high-quality, relevant data. We can help you with data strategy and acquisition if you don't have the necessary data on hand."
      },
      {
        question: "How long does a typical AI project take?",
        answer: "Project timelines vary based on complexity, data availability, and the scope of work. A pilot project can take a few weeks, while a full-scale enterprise solution may take several months. We provide a detailed timeline after the initial discovery phase."
      },
      {
        question: "How do you ensure the AI model is accurate?",
        answer: "We use rigorous testing and validation techniques on historical data. After deployment, we continuously monitor the model's performance against live data and retrain it as needed to maintain high accuracy and adapt to new patterns."
      }
    ]
  },
  cta: {
    title: "Ready to Build the Future?",
    text: "Let's discuss how our AI expertise can be tailored to meet your unique business goals.",
    buttonText: "Schedule a Consultation",
    buttonLink: "/contact"
  }
};


// --- THE REACT COMPONENT ---
const AIPage = () => {
  const [key, setKey] = useState('ml');

  return (
    <>
      <style>{`
        /* --- General & Reusable Styles --- */
        .section-title { font-weight: 700; margin-bottom: 1rem; }
        .section-description { color: #6c757d; margin-bottom: 3rem; }
        .bg-light-blue { background-color: #f0f7ff; }
        .feature-list-item { display: flex; align-items: center; gap: 10px; }
        .feature-list-item .fa-check-circle { color: #28a745; }

        /* --- Hero Section --- */
        @keyframes hero-float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
        }

        /* --- Solutions (Tabs) Section --- */
        .solutions-tabs .nav-link { font-weight: 600; color: #6c757d; border: none; border-bottom: 3px solid transparent; }
        .solutions-tabs .nav-link.active { color: #007bff; border-bottom: 3px solid #007bff; background-color: transparent; }
        .solutions-tabs .tab-content { padding-top: 2rem; }
        .solution-image { width: 100%; height: 300px; object-fit: cover; border-radius: 1rem; }
        
        /* --- NEW: Process (Numbered Cards) Section --- */
        .process-card {
            border: none;
            border-radius: .5rem;
            position: relative;
            padding-left: 3.5rem;
        }
        .process-step-number {
            position: absolute;
            top: 1.5rem;
            left: 1.5rem;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e7f1ff;
            color: #007bff;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* --- Industries Section --- */
        .industry-card { border: none; transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .industry-card:hover { transform: translateY(-5px); box-shadow: 0 10px 30px rgba(0,0,0,0.08); }
        .industry-icon { font-size: 2.5rem; color: #007bff; }

        /* --- NEW: FAQ (Grid) Section --- */
        .faq-item { margin-bottom: 2rem; }
        .faq-question { font-weight: 600; color: #007bff; }
        .faq-answer { color: #6c757d; }
      `}</style>
      
      {/* ======================================= HERO SECTION ======================================= */}
      <div style={{ position: 'relative', minHeight: '100vh', display: 'flex', alignItems: 'center', background: 'linear-gradient(135deg, #002956 0%, #001a3a 100%)', color: 'white' }}>
        <div style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, backgroundImage: `url("${aiPageData.hero.backgroundImage}")`, backgroundSize: 'cover', backgroundPosition: 'center', opacity: 0.15 }}></div>
        <Container>
          <Row className="align-items-center">
            <Col lg={6} style={{ zIndex: 2 }}>
              <h1 className="display-4 fw-bold">{aiPageData.hero.title}</h1>
              <p className="lead my-4">{aiPageData.hero.subtitle}</p>
              <Button as={Link} to="/services" variant="primary" size="lg" className="me-3">Explore AI Solutions</Button>
              <Button as={Link} to="/contact" variant="outline-light" size="lg">Contact Us</Button>
            </Col>
            <Col lg={6} className="d-none d-lg-block"><div style={{ position: 'relative', perspective: '1000px' }}><div style={{ height: '350px', background: 'rgba(255, 255, 255, 0.05)', borderRadius: '20px', backdropFilter: 'blur(10px)', border: '1px solid rgba(255, 255, 255, 0.1)', boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)', transform: 'rotateY(-15deg) rotateX(10deg)', animation: 'hero-float 8s ease-in-out infinite' }}></div></div></Col>
          </Row>
        </Container>
      </div>

      {/* ======================================= TABS UI PATTERN ======================================= */}
      <Container as="section" className="py-5">
          <Row className="text-center"><Col lg={8} className="mx-auto"><h2 className="section-title">{aiPageData.solutions.title}</h2><p className="section-description">{aiPageData.solutions.description}</p></Col></Row>
          <Tabs activeKey={key} onSelect={(k) => setKey(k)} id="solutions-tabs" className="justify-content-center solutions-tabs mb-3">
              {aiPageData.solutions.items.map((solution) => (
                  <Tab eventKey={solution.eventKey} title={solution.title} key={solution.eventKey}>
                      <Row className="align-items-center g-4 mt-3"><Col lg={6}><img src={solution.image} alt={solution.title} className="solution-image" /></Col><Col lg={6}><h4 className="fw-bold">{solution.title}</h4><p className="text-muted">{solution.text}</p><ul className="list-unstyled mt-3">{solution.services.map((service, i) => (<li key={i} className="mb-2 feature-list-item"><i className="fas fa-check-circle"></i><span>{service}</span></li>))}</ul></Col></Row>
                  </Tab>
              ))}
          </Tabs>
      </Container>
      
      {/* ======================================= REVISED: NUMBERED CARDS UI PATTERN ======================================= */}
      <div className="bg-light-blue">
        <Container as="section" className="py-5">
          <Row className="text-center"><Col lg={8} className="mx-auto"><h2 className="section-title">{aiPageData.process.title}</h2><p className="section-description">{aiPageData.process.description}</p></Col></Row>
          <Row className="g-4 justify-content-center">
            {aiPageData.process.steps.map((step, index) => (
              <Col lg={10} key={index}>
                <Card className="p-3 process-card">
                  <div className="process-step-number">{index + 1}</div>
                  <Card.Body>
                    <h5 className="fw-bold">{step.title}</h5>
                    <p className="mb-0 text-muted">{step.text}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </div>

      {/* ======================================= GRID OF CARDS UI PATTERN ======================================= */}
      <Container as="section" className="py-5">
        <Row className="text-center"><Col lg={8} className="mx-auto"><h2 className="section-title">{aiPageData.industries.title}</h2><p className="section-description">{aiPageData.industries.description}</p></Col></Row>
        <Row>
          {aiPageData.industries.items.map((industry, index) => (
            <Col md={6} lg={3} key={index} className="mb-4"><Card className="h-100 text-center p-4 industry-card"><Card.Body><i className={`${industry.icon} industry-icon mb-3`}></i><h5 className="fw-bold">{industry.name}</h5></Card.Body></Card></Col>
          ))}
        </Row>
      </Container>

      {/* ======================================= REVISED: Q&A GRID UI PATTERN ======================================= */}
      <div className="bg-light">
        <Container as="section" className="py-5">
          <Row className="text-center"><Col lg={8} className="mx-auto"><h2 className="section-title">{aiPageData.faq.title}</h2></Col></Row>
          <Row className="justify-content-center mt-4">
            <Col lg={10}>
              {aiPageData.faq.items.map((item, index) => (
                <div className="faq-item" key={index}>
                  <Row className="g-3">
                    <Col md={4}><h6 className="faq-question">{item.question}</h6></Col>
                    <Col md={8}><p className="faq-answer mb-0">{item.answer}</p></Col>
                  </Row>
                </div>
              ))}
            </Col>
          </Row>
        </Container>
      </div>
     
    </>
  );
};

export default AIPage;
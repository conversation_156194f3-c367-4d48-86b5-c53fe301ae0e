import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Card, Accordion } from 'react-bootstrap';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";

gsap.registerPlugin(ScrollTrigger);

// --- ENHANCED DATA FOR THE PAGE ---
const aiPageData = {
  hero: {
    title: "Intelligent Solutions for Tomorrow",
    subtitle: "Harness the power of Artificial Intelligence to transform your business operations, automate complex processes, and unlock unprecedented insights from your data.",
    backgroundImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  solutions: {
    title: "Our AI Solutions",
    description: "We offer a comprehensive suite of AI services. Explore our capabilities to see how we can drive your business forward.",
    items: [
      {
        eventKey: "ml",
        title: "Machine Learning",
        text: "Our Machine Learning solutions focus on creating systems that learn and adapt from data. We build custom models to automate processes, predict outcomes, and provide actionable insights that give you a competitive edge.",
        services: ["Predictive Analytics & Forecasting", "Recommendation Engines", "Custom Algorithm Development", "Data Clustering & Classification"],
        image: "https://images.unsplash.com/photo-1633409361619-b7b2524a3c40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
      },
      {
        eventKey: "cv",
        title: "Computer Vision",
        text: "We empower machines to see and interpret the world. Our computer vision services automate tasks that typically require human vision, such as quality control, surveillance, and medical image analysis, with superhuman accuracy and speed.",
        services: ["Object Detection & Tracking", "Facial Recognition Systems", "Image & Video Analysis", "Automated Quality Inspection"],
        image: "https://images.unsplash.com/photo-1579551065094-13a83a04c219?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
      },
      {
        eventKey: "nlp",
        title: "Natural Language Processing",
        text: "Unlock the value of unstructured text data. Our NLP solutions enable machines to understand and process human language, powering everything from intelligent chatbots and virtual assistants to sentiment analysis and document summarization.",
        services: ["Sentiment Analysis", "Intelligent Chatbots", "Text Summarization", "Document Processing & Extraction"],
        image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80"
      }
    ]
  },
  whyChooseUs: {
    title: "Why Partner With Us?",
    description: "We deliver more than just code. We deliver real business value through a combination of technical expertise, strategic thinking, and a commitment to your success.",
    items: [
        {
            icon: "fas fa-handshake",
            title: "Collaborative Partnership",
            text: "We work closely with you at every stage, ensuring our AI solutions are perfectly aligned with your business objectives and deliver measurable ROI."
        },
        {
            icon: "fas fa-cogs",
            title: "Custom-Built Solutions",
            text: "We don't believe in one-size-fits-all. Every model we build is tailored to your unique data, challenges, and goals for maximum impact."
        },
        {
            icon: "fas fa-shield-alt",
            title: "Ethical & Secure AI",
            text: "We are committed to building responsible AI. Our practices prioritize fairness, transparency, and robust data security in every project."
        },
    ]
  },
  process: {
    title: "Our Development Process",
    description: "We follow a structured, agile methodology to ensure project success and deliver measurable results.",
    steps: [
      {
        icon: "fas fa-search",
        title: "Discovery & Consultation",
        text: "We start by understanding your goals and challenges to define a clear AI strategy and project roadmap."
      },
      {
        icon: "fas fa-database",
        title: "Data Strategy & Preparation",
        text: "Our team collects, cleans, and processes your data to create high-quality datasets ready for model training."
      },
      {
        icon: "fas fa-brain",
        title: "Model Development",
        text: "Using the prepared data, we build and train custom AI models tailored to your specific requirements."
      },
      {
        icon: "fas fa-rocket",
        title: "Deployment & Integration",
        text: "We seamlessly deploy the AI model into your existing systems and workflows with minimal disruption."
      },
      {
        icon: "fas fa-chart-line",
        title: "Monitoring & Optimization",
        text: "Post-deployment, we continuously monitor performance and retrain the model to ensure long-term accuracy and value."
      }
    ]
  },
  industries: {
    title: "Industries We Serve",
    description: "Our AI solutions are versatile and can be adapted to drive innovation across a wide range of industries.",
    items: [
      { icon: "fas fa-heartbeat", name: "Healthcare", description: "From predictive diagnostics to personalized treatment plans, we help healthcare providers improve patient outcomes and operational efficiency." },
      { icon: "fas fa-dollar-sign", name: "Finance & Banking", description: "Enhance security with fraud detection, automate loan processing, and provide personalized financial advice with our AI-powered solutions." },
      { icon: "fas fa-shopping-cart", name: "E-commerce & Retail", description: "Optimize supply chains, personalize customer experiences with recommendation engines, and analyze market trends to stay ahead." },
      { icon: "fas fa-industry", name: "Manufacturing", description: "Implement predictive maintenance to reduce downtime, automate quality control, and optimize production lines for maximum output." }
    ]
  },
  faq: {
    title: "Frequently Asked Questions",
    items: [
      {
        question: "What kind of data do I need for an AI project?",
        answer: "The data required depends on the problem you're trying to solve. Typically, you need a substantial amount of high-quality, relevant data. We can help you with data strategy and acquisition if you don't have the necessary data on hand."
      },
      {
        question: "How long does a typical AI project take?",
        answer: "Project timelines vary based on complexity, data availability, and the scope of work. A pilot project can take a few weeks, while a full-scale enterprise solution may take several months. We provide a detailed timeline after the initial discovery phase."
      },
      {
        question: "How do you ensure the AI model is accurate?",
        answer: "We use rigorous testing and validation techniques on historical data. After deployment, we continuously monitor the model's performance against live data and retrain it as needed to maintain high accuracy and adapt to new patterns."
      },
      {
        question: "What kind of post-deployment support do you offer?",
        answer: "Our partnership doesn't end at deployment. We offer comprehensive support packages that include performance monitoring, regular model retraining, and on-call assistance to ensure your AI solution continues to deliver peak value."
      }
    ]
  },
  cta: {
    title: "Ready to Build the Future?",
    text: "Let's discuss how our AI expertise can be tailored to meet your unique business goals.",
    buttonText: "Schedule a Consultation",
    buttonLink: "/contact"
  }
};


// --- THE ENHANCED REACT COMPONENT ---
const AIPage = () => {
  const [activeCard, setActiveCard] = useState(0);
  const [isVisible, setIsVisible] = useState({});
  const [stats, setStats] = useState({ projects: 0, accuracy: 0, clients: 0, models: 0 });
  const [neuralNodes, setNeuralNodes] = useState([]);
  const heroRef = useRef(null);
  const statsRef = useRef(null);
  const processRef = useRef(null);
  const networkRef = useRef(null);
  const pageWrapperRef = useRef(null);

  // Initialize and animate neural network
  useEffect(() => {
    const nodes = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      speed: (Math.random() - 0.5) * 0.2,
      connections: []
    }));

    nodes.forEach((node, i) => {
      nodes.forEach((otherNode, j) => {
        if (i !== j) {
          const distance = Math.sqrt(Math.pow(node.x - otherNode.x, 2) + Math.pow(node.y - otherNode.y, 2));
          if (distance < 20 && node.connections.length < 3) {
            node.connections.push(j);
          }
        }
      });
    });
    setNeuralNodes(nodes);

    let animationFrameId;
    const animate = () => {
        setNeuralNodes(prev => prev.map(node => ({
            ...node,
            x: (node.x + node.speed + 100) % 100,
            y: (node.y + Math.sin(Date.now() * 0.0005 + node.id) * 0.1 + 100) % 100
        })));
        animationFrameId = requestAnimationFrame(animate);
    };
    animate();

    return () => cancelAnimationFrame(animationFrameId);
  }, []);

  // GSAP Animations
  useEffect(() => {
    window.scrollTo(0, 0);

    const ctx = gsap.context(() => {
        // Hero section animations
        gsap.from(heroRef.current.querySelector('h1'), { y: 100, opacity: 0, duration: 1.2, ease: "power3.out" });
        gsap.from(heroRef.current.querySelector('p'), { y: 50, opacity: 0, duration: 1, ease: "power2.out", delay: 0.3 });

        // Reusable card animation
        const animateCards = (selector) => {
            gsap.utils.toArray(selector).forEach(card => {
                gsap.from(card, {
                    y: 100,
                    opacity: 0,
                    duration: 0.8,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: card,
                        start: "top 85%",
                        toggleActions: "play none none reverse",
                    }
                });
            });
        };
        
        animateCards('.animated-card');

    }, pageWrapperRef);

    return () => ctx.revert();
  }, []);

  // Reusable Style Objects
  const primaryColor = "#007bff";
  const primaryDarkColor = "#0056b3";
  const primaryRgb = "0,123,255";
  const glassEffect = {
      background: "rgba(255, 255, 255, 0.05)",
      backdropFilter: "blur(20px)",
      border: "1px solid rgba(255, 255, 255, 0.1)",
      transition: "all 0.3s ease-in-out"
  };

  return (
    <div className="ai-page-wrapper" ref={pageWrapperRef} style={{ background: "#001020" }}>
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="ai-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 20, 40, 0.85), rgba(0, 10, 20, 0.98)), url(${aiPageData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        <div className="position-absolute w-100 h-100 top-0 start-0" style={{ zIndex: 0, opacity: 0.5 }}>
            <svg width="100%" height="100%">
                {neuralNodes.map(node => (
                    <g key={node.id}>
                        {node.connections.map(connId => {
                            const connectedNode = neuralNodes[connId];
                            if (!connectedNode) return null;
                            return <line key={`${node.id}-${connId}`} x1={`${node.x}%`} y1={`${node.y}%`} x2={`${connectedNode.x}%`} y2={`${connectedNode.y}%`} stroke="rgba(0, 160, 233, 0.2)" strokeWidth="1"/>
                        })}
                    </g>
                ))}
                {neuralNodes.map(node => (
                    <g key={node.id}>
                        <circle cx={`${node.x}%`} cy={`${node.y}%`} r={node.size} fill="rgba(0, 160, 233, 0.8)"/>
                    </g>
                ))}
            </svg>
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
            <h1 className="display-1 fw-bolder mb-4" style={{ background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)", WebkitBackgroundClip: "text", WebkitTextFillColor: "transparent", textShadow: "0 0 30px rgba(0, 160, 233, 0.3)" }}>
                {aiPageData.hero.title}
            </h1>
            <p className="lead mb-5 mx-auto" style={{ maxWidth: "1000px", textShadow: "1px 1px 3px rgba(0,0,0,0.4)", fontSize: "1.35rem" }}>
                {aiPageData.hero.subtitle}
            </p>
        </Container>
      </section>
      
      {/* --- NEW: Why Choose Us Section --- */}
      <section className="py-5 text-white" style={{ background: "rgba(0,10,20,0.9)" }}>
          <Container>
            <Row className="text-center mb-5">
              <Col>
                <h2 className="display-4 fw-bold" style={{ color: "#eee" }}>{aiPageData.whyChooseUs.title}</h2>
                <p className="lead text-white-50 mx-auto" style={{maxWidth: "800px"}}>{aiPageData.whyChooseUs.description}</p>
              </Col>
            </Row>
            <Row>
              {aiPageData.whyChooseUs.items.map((item, index) => (
                <Col md={4} key={index} className="mb-4 animated-card">
                  <Card className="h-100 text-center text-white p-4 why-us-card" style={{ ...glassEffect, borderRadius: "20px" }}>
                      <Card.Body>
                          <div className="mb-4">
                              <i className={`${item.icon} fa-3x`} style={{ color: "#00a0e9" }}></i>
                          </div>
                          <Card.Title as="h3" className="mb-3">{item.title}</Card.Title>
                          <Card.Text className="text-white-50">
                              {item.text}
                          </Card.Text>
                      </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </Container>
      </section>

      {/* --- REVAMPED: Our Process Section --- */}
      <section ref={processRef} className="py-5 text-white" style={{ background: "linear-gradient(rgba(0, 10, 20, 0.9), rgba(0, 20, 40, 0.95))" }}>
          <Container>
              <Row className="text-center mb-5">
                  <Col>
                      <h2 className="display-4 fw-bold" style={{ color: "#eee" }}>{aiPageData.process.title}</h2>
                      <p className="lead text-white-50 mx-auto" style={{maxWidth: "800px"}}>{aiPageData.process.description}</p>
                  </Col>
              </Row>
              <Row>
                  {aiPageData.process.steps.map((step, index) => (
                      <Col key={index} md={4} lg className="mb-4 d-flex align-items-stretch animated-card">
                          <div className="process-step-card d-flex flex-column align-items-center text-center p-4 w-100" style={{...glassEffect, borderRadius: '20px'}}>
                              <div className="process-step-number mb-3">
                                  <span>0{index + 1}</span>
                              </div>
                              <i className={`${step.icon} fa-2x mb-3`} style={{color: '#00a0e9'}}></i>
                              <h4 className="mb-3 text-white">{step.title}</h4>
                              <p className="text-white-50 mb-0">{step.text}</p>
                          </div>
                      </Col>
                  ))}
              </Row>
          </Container>
      </section>

      {/* --- EXPANDED: Industries Section --- */}
      <section className="py-5 text-white" style={{ background: "rgba(0,10,20,0.9)" }}>
          <Container>
              <Row className="text-center mb-5">
                  <Col>
                      <h2 className="display-4 fw-bold" style={{ color: "#eee" }}>{aiPageData.industries.title}</h2>
                      <p className="lead text-white-50 mx-auto" style={{maxWidth: "800px"}}>{aiPageData.industries.description}</p>
                  </Col>
              </Row>
              <Row>
                  {aiPageData.industries.items.map((industry, index) => (
                      <Col md={6} lg={3} key={index} className="mb-4 animated-card">
                          <Card className="h-100 text-white industry-card" style={{ ...glassEffect, borderRadius: '20px' }}>
                              <Card.Body className="d-flex flex-column text-center align-items-center">
                                  <div className="mb-3">
                                      <i className={`${industry.icon} fa-3x`} style={{ color: "#00a0e9" }}></i>
                                  </div>
                                  <Card.Title as="h4" className="mb-3">{industry.name}</Card.Title>
                                  <Card.Text className="text-white-50">
                                      {industry.description}
                                  </Card.Text>
                              </Card.Body>
                          </Card>
                      </Col>
                  ))}
              </Row>
          </Container>
      </section>

      {/* --- ENHANCED: FAQ Section --- */}
      <section className="py-5 text-white" style={{ background: "linear-gradient(rgba(0, 20, 40, 0.95), rgba(0, 10, 20, 0.98))" }}>
          <Container>
              <Row className="text-center mb-5">
                  <Col>
                      <h2 className="display-4 fw-bold" style={{ color: "#eee" }}>{aiPageData.faq.title}</h2>
                  </Col>
              </Row>
              <Row className="justify-content-center">
                  <Col lg={8}>
                      <Accordion defaultActiveKey="0" className="faq-accordion">
                          {aiPageData.faq.items.map((item, index) => (
                              <Card key={index} className="mb-3 animated-card" style={{...glassEffect, borderRadius: '15px'}}>
                                  <Accordion.Item eventKey={String(index)} className="border-0">
                                      <Accordion.Header as={Card.Header} style={{ background: 'transparent', color: 'white' }}>
                                          {item.question}
                                      </Accordion.Header>
                                      <Accordion.Body style={{ color: 'rgba(255,255,255,0.7)' }}>
                                          {item.answer}
                                      </Accordion.Body>
                                  </Accordion.Item>
                              </Card>
                          ))}
                      </Accordion>
                  </Col>
              </Row>
          </Container>
      </section>

      {/* Global CSS */}
      <style>{`
        :root {
          --bs-primary: ${primaryColor};
          --bs-primary-dark: ${primaryDarkColor};
          --bs-primary-rgb: ${primaryRgb};
        }

        .why-us-card:hover, .industry-card:hover, .process-step-card:hover {
            transform: translateY(-10px);
            background: rgba(0, 160, 233, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .process-step-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid rgba(0, 160, 233, 0.5);
            color: rgba(0, 160, 233, 0.8);
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .process-step-card:hover .process-step-number {
            background: #00a0e9;
            color: white;
            border-color: #00a0e9;
            transform: scale(1.1);
        }
        
        .faq-accordion .accordion-item {
            background: transparent;
        }
        .faq-accordion .accordion-header button {
            background: transparent;
            color: #fff;
            font-weight: bold;
            border: none;
            box-shadow: none;
        }
        .faq-accordion .accordion-header button:not(.collapsed) {
            color: #00a0e9;
            background: rgba(0, 160, 233, 0.1);
        }
        .faq-accordion .accordion-header button::after {
            filter: invert(1) grayscale(100%) brightness(200%);
        }

        .cta-bg-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(0, 160, 233, 0.2) 0%, rgba(0, 160, 233, 0) 70%);
            transform: translate(-50%, -50%);
            animation: pulse-glow 5s infinite ease-in-out;
            z-index: 1;
        }

        @keyframes pulse-glow {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
            50% { transform: translate(-50%, -50%) scale(1.5); opacity: 1; }
        }

        .cta-button {
            padding: 0.8rem 2rem;
            font-size: 1.1rem;
            font-weight: bold;
            border-radius: 50px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #00a0e9, #0056b3);
            border: none;
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 160, 233, 0.3);
        }
      `}</style>
    </div>
  );
};

export default AIPage;
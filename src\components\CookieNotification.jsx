import React, { useEffect, useRef } from 'react';
import { useCookieConsent } from '../hooks/useCookieConsent';
import { gsap } from 'gsap';

// Development helper - Add to window for testing
if (typeof window !== 'undefined') {
  window.resetCookieConsent = () => {
    localStorage.removeItem('makonis_cookie_consent');
    sessionStorage.removeItem('makonis_banner_shown_session');
    window.location.reload();
  };

  // Helper to show banner in current tab (for testing)
  window.showCookieBanner = () => {
    sessionStorage.removeItem('makonis_banner_shown_session');
    window.location.reload();
  };
}

const CookieNotification = () => {
  const { showBanner, acceptCookies, isLoading } = useCookieConsent();
  const bannerRef = useRef(null);

  useEffect(() => {
    if (showBanner && bannerRef.current && !isLoading) {
      // Animate banner entrance
      gsap.fromTo(
        bannerRef.current,
        { 
          y: 100, 
          opacity: 0,
          scale: 0.95
        },
        { 
          y: 0, 
          opacity: 1,
          scale: 1,
          duration: 0.6,
          ease: "back.out(1.7)",
          delay: 0.5 // Small delay to ensure page is loaded
        }
      );
    }
  }, [showBanner, isLoading]);

  const handleAccept = () => {
    // Animate banner exit
    gsap.to(bannerRef.current, {
      y: 100,
      opacity: 0,
      scale: 0.95,
      duration: 0.4,
      ease: "power2.in",
      onComplete: () => {
        acceptCookies();
      }
    });
  };


  // Don't render if loading or banner shouldn't show
  if (isLoading || !showBanner) {
    return null;
  }

  return (
    <div
      ref={bannerRef}
      className="position-fixed w-100 d-flex justify-content-center align-items-center"
      style={{
        bottom: '20px',
        left: '0',
        right: '0',
        zIndex: 10000,
        padding: '0 20px',
        pointerEvents: 'none'
      }}
    >
      <div
        className="d-flex flex-column flex-md-row align-items-center justify-content-between p-4 rounded-4 shadow-lg"
        style={{
          background: 'rgba(0, 41, 86, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          maxWidth: '900px',
          width: '100%',
          pointerEvents: 'auto',
          boxShadow: '0 20px 60px rgba(0, 41, 86, 0.4)'
        }}
      >
        {/* Cookie Icon */}
        <div className="d-flex align-items-center mb-3 mb-md-0 me-md-4">
          <div
            className="d-flex align-items-center justify-content-center me-3"
            style={{
              width: '48px',
              height: '48px',
              background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
              borderRadius: '12px',
              boxShadow: '0 8px 25px rgba(0, 160, 233, 0.3)'
            }}
          >
            <i className="fas fa-cookie-bite text-white" style={{ fontSize: '1.5rem' }}></i>
          </div>
          
          {/* Message */}
          <div className="text-white">
            <h6 className="mb-1 fw-semibold" style={{ fontSize: '1.1rem' }}>
              We use cookies
            </h6>
            <p className="mb-0 text-white-50" style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
              This website uses cookies to ensure you get the best experience on our website.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="d-flex flex-column flex-sm-row gap-2 align-items-center">
          
          <button
            onClick={handleAccept}
            className="btn text-white fw-semibold px-4 py-2 rounded-3"
            style={{
              background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
              border: 'none',
              fontSize: '1rem',
              minWidth: '120px',
              boxShadow: '0 4px 15px rgba(0, 160, 233, 0.3)',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 6px 20px rgba(0, 160, 233, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 4px 15px rgba(0, 160, 233, 0.3)';
            }}
          >
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieNotification;

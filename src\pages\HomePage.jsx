import React, { useEffect, useRef } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ServicesSection from '../components/ServicesSection';
import ClientsSection from '../components/ClientsSection';
import QuickTestingSection from '../components/QuickTestingSection';
import homePageVideo from '../asserts/homepagevideo.mp4';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const HomePage = () => {
    // Refs for animation targets
    const heroRef = useRef(null);
    const titleRef = useRef(null);
    const subtitleRef = useRef(null);



    // Hero section animations with slide-in effects
     useEffect(() => {
       const ctx = gsap.context(() => {
         const tl = gsap.timeline({ delay: 0.2 });
   
         // Title slides in from left with rotation
         if (titleRef.current) {
           tl.from(titleRef.current, {
             x: -200,
             opacity: 0,
             rotation: -10,
             duration: 1.5,
             ease: "power3.out"
           });
         }
   
         // Subtitle slides in from right
         if (subtitleRef.current) {
           tl.from(subtitleRef.current, {
             x: 200,
             opacity: 0,
             duration: 1.2,
             ease: "power2.out"
           }, "-=1");
         }
   

       }, heroRef);
   
       return () => ctx.revert();
     }, []);

    return (
        <>
            {/* Loading Screen */}
            {/* {isLoading && (
                <StunningLoadingScreen ref={loadingRef} loadingProgress={loadingProgress} />
            )} */}

            {/* Stunning Hero Section */}
            <section>
                <style>{`
                       .ats-demo-page {
                         overflow-x: hidden;
                       }
               
                       .hero-section {
                         position: relative;
                       }
               
                       .hero-section::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: 0;
                         right: 0;
                         bottom: 0;
                         background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
                         opacity: 0.3;
                         pointer-events: none;
                       }
               
                       /* --- Animations --- */
                       @keyframes fadeInUp {
                         from {
                           opacity: 0;
                           transform: translateY(30px);
                         }
                         to {
                           opacity: 1;
                           transform: translateY(0);
                         }
                       }
               
                       @keyframes pulse {
                           0% {
                               transform: translate(-50%, -50%) scale(1);
                               opacity: 0.8;
                               box-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
                           }
                           50% {
                               transform: translate(-50%, -50%) scale(1.05);
                               opacity: 1;
                               box-shadow: 0 0 70px rgba(0, 160, 233, 0.6);
                           }
                           100% {
                               transform: translate(-50%, -50%) scale(1);
                               opacity: 0.8;
                               box-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
                           }
                       }
               
                       @keyframes orbit {
                         from { transform: translate(-50%, -50%) rotate(0deg) translateX(var(--radius)) rotate(0deg); }
                         to { transform: translate(-50%, -50%) rotate(360deg) translateX(var(--radius)) rotate(-360deg); }
                       }
               
                       @keyframes dash {
                         to { stroke-dashoffset: -20; }
                       }
               
                       @keyframes particleFloat {
                           0%, 100% {
                               transform: translateY(0px) translateX(0px) rotate(0deg);
                               opacity: 0.6;
                           }
                           33% {
                               transform: translateY(-20px) translateX(10px) rotate(120deg);
                               opacity: 1;
                           }
                           66% {
                               transform: translateY(10px) translateX(-15px) rotate(240deg);
                               opacity: 0.8;
                           }
                       }
               
                       @keyframes float {
                           0%, 100% {
                               transform: translateY(0px) translateX(0px) rotate(0deg);
                               opacity: 0.7;
                           }
                           33% {
                               transform: translateY(-25px) translateX(15px) rotate(100deg);
                               opacity: 1;
                           }
                           66% {
                               transform: translateY(10px) translateX(-20px) rotate(200deg);
                               opacity: 0.8;
                           }
                       }
               
                       /* --- Hero Section Styles --- */
                       .hero-section {
                         background-attachment: fixed;
                         background-size: cover;
                         background-position: center;
                       }
               
                       .display-1 {
                           letter-spacing: -1px;
                       }
               
                       .lead {
                           font-weight: 300;
                       }
               
                       /* Hero stats animation delay */
                       .hero-section > .container > .row > .col-lg-6 {
                         animation: fadeInUp 0.8s ease-out;
                       }
               
                       .hero-section > .container > .row > .col-lg-6:nth-child(2) {
                         animation-delay: 0.2s;
                       }
               
                       /* --- Video Section Styles --- */
                       .video-section {
                         padding: 3rem 0;
                       }
               
                       .video-section .col-lg-10 {
                         animation: fadeInUp 0.8s ease-out 0.4s both;
                       }
               
                       /* Ensure video section doesn't overflow */
                       .video-section .container {
                         max-width: 100%;
                         padding: 0 15px;
                       }
               
                       .video-container {
                           background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
                           aspect-ratio: 16/9;
                           max-height: 80vh; /* Responsive height constraint */
                           width: 100%;
                           box-shadow: 0 20px 60px rgba(0, 43, 89, 0.3), 0 8px 25px rgba(0, 157, 230, 0.2);
                           border: 2px solid rgba(0, 157, 230, 0.1);
                           position: relative;
                           overflow: hidden;
                       }
               
                       /* Responsive video adjustments */
                       @media (max-width: 768px) {
                         .video-container {
                           max-height: 70vh;
                           aspect-ratio: 16/9;
                         }
                       }
               
                       @media (max-width: 576px) {
                         .video-container {
                           max-height: 60vh;
                           aspect-ratio: 16/9;
                         }
                       }
               
                       .video-container::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: 0;
                         right: 0;
                         bottom: 0;
                         background: linear-gradient(45deg, transparent 30%, rgba(0, 157, 230, 0.05) 50%, transparent 70%);
                         pointer-events: none;
                         z-index: 1;
                       }
               
                       /* Video controls */
                       .video-controls {
                         opacity: 0;
                         transform: translateY(20px);
                         transition: all 0.4s ease-out;
                         pointer-events: none; /* Allows clicks through when hidden */
                         backdrop-filter: blur(15px);
                         background: linear-gradient(transparent, rgba(0,0,0,0.9)) !important;
                       }
               
                       .video-container:hover .video-controls {
                         opacity: 1;
                         transform: translateY(0);
                         pointer-events: all; /* Enable clicks when visible */
                       }
               
                       /* Play/Pause, Skip buttons */
                       .video-controls .btn {
                           font-size: 1.2rem;
                           background: rgba(255, 255, 255, 0.1);
                           border: 1px solid rgba(255, 255, 255, 0.2);
                           width: 45px;
                           height: 45px;
                           transition: all 0.3s ease;
                       }
               
                       .video-controls .btn:hover {
                           background: rgba(0, 157, 230, 0.8) !important;
                           transform: scale(1.1);
                       }
               
                       /* Progress bar */
                       .progress {
                           height: 8px !important; /* Make it slightly taller for better clickability */
                           cursor: pointer;
                           background: rgba(255, 255, 255, 0.2);
                           border-radius: 3px;
                           transition: all 0.3s ease-in-out !important; /* Smooth transition for height and scale */
                       }
               
                       .progress:hover {
                           transform: scaleY(1.5);
                       }
               
                       .progress-bar {
                           background: linear-gradient(90deg, #002B59 0%, #009DE6 100%) !important;
                           border-radius: 3px;
                           transition: width 0.1s ease;
                       }
               
                       .progress-bar::after { /* Add a subtle glow to the scrubber thumb */
                           content: '';
                           position: absolute;
                           top: 50%;
                           left: 100%; /* Relative to the progress-bar itself */
                           transform: translate(-50%, -50%);
                           width: 12px;
                           height: 12px;
                           background: #fff;
                           box-shadow: 0 0 10px rgba(0, 157, 230, 0.7); /* Subtle blue glow */
                           border-radius: 50%;
                           opacity: 1; /* Always visible */
                           pointer-events: none;
                           z-index: 1;
                       }
               
                       /* Enhanced Play button overlay */
                       .video-play-overlay {
                           background: linear-gradient(135deg, rgba(0, 160, 233, 0.95) 0%, rgba(0, 86, 179, 0.95) 100%);
                           backdrop-filter: blur(20px);
                           border: 3px solid rgba(255, 255, 255, 0.3);
                           box-shadow:
                               0 0 0 0 rgba(0, 160, 233, 0.7),
                               0 25px 50px rgba(0, 43, 89, 0.4),
                               0 10px 25px rgba(0, 0, 0, 0.3),
                               inset 0 1px 0 rgba(255, 255, 255, 0.2);
                           width: 100px;
                           height: 100px;
                           transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
                           animation: playButtonPulse 3s ease-in-out infinite;
                           position: relative;
                           overflow: hidden;
                       }
               
                       .video-play-overlay::before {
                           content: '';
                           position: absolute;
                           top: -50%;
                           left: -50%;
                           width: 200%;
                           height: 200%;
                           background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
                           transform: rotate(-45deg);
                           transition: transform 0.6s ease;
                       }
               
                       .video-play-overlay:hover::before {
                           transform: rotate(-45deg) translateX(100%);
                       }
               
                       .video-play-overlay:hover {
                           transform: scale(1.15);
                           box-shadow:
                               0 0 0 20px rgba(0, 160, 233, 0.2),
                               0 30px 60px rgba(0, 43, 89, 0.5),
                               0 15px 35px rgba(0, 0, 0, 0.4),
                               inset 0 1px 0 rgba(255, 255, 255, 0.3);
                           border-color: rgba(255, 255, 255, 0.5);
                       }
               
                       .video-play-overlay:active {
                           transform: scale(1.05);
                       }
               
                       /* Play button pulse animation */
                       @keyframes playButtonPulse {
                           0%, 100% {
                               box-shadow:
                                   0 0 0 0 rgba(0, 160, 233, 0.7),
                                   0 25px 50px rgba(0, 43, 89, 0.4),
                                   0 10px 25px rgba(0, 0, 0, 0.3),
                                   inset 0 1px 0 rgba(255, 255, 255, 0.2);
                           }
                           50% {
                               box-shadow:
                                   0 0 0 15px rgba(0, 160, 233, 0.3),
                                   0 25px 50px rgba(0, 43, 89, 0.4),
                                   0 10px 25px rgba(0, 0, 0, 0.3),
                                   inset 0 1px 0 rgba(255, 255, 255, 0.2);
                           }
                       }
               
                       /* Pulse rings around play button */
                       .play-button-ring {
                           position: absolute;
                           border: 2px solid rgba(0, 160, 233, 0.4);
                           border-radius: 50%;
                           animation: playButtonRings 2s ease-out infinite;
                       }
               
                       .play-button-ring:nth-child(1) {
                           width: 120px;
                           height: 120px;
                           animation-delay: 0s;
                       }
               
                       .play-button-ring:nth-child(2) {
                           width: 150px;
                           height: 150px;
                           animation-delay: 0.5s;
                       }
               
                       .play-button-ring:nth-child(3) {
                           width: 180px;
                           height: 180px;
                           animation-delay: 1s;
                       }
               
                       @keyframes playButtonRings {
                           0% {
                               transform: translate(-50%, -50%) scale(0.8);
                               opacity: 1;
                           }
                           100% {
                               transform: translate(-50%, -50%) scale(1.2);
                               opacity: 0;
                           }
                       }
               
                       /* --- Features Section Styles (Refined) --- */
                       .features-section {
                         padding-top: 5rem; /* More padding for separation */
                         padding-bottom: 5rem; /* More padding for separation */
                         background-color: #f8f9fa; /* Light background for contrast */
                       }
               
                       .feature-card {
                           backdrop-filter: blur(10px); /* If you want this on a lighter background, it might need adjustment or removal */
                           background: white;
                           border: 1px solid rgba(0, 157, 230, 0.1) !important;
                           box-shadow: 0 4px 15px rgba(0, 43, 89, 0.06);
                           transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                           position: relative;
                           overflow: hidden;
                           animation: fadeInUp 0.6s ease-out both;
                           padding: 1.5rem; /* Adjust padding for cards */
                           border-radius: 0.75rem; /* More rounded corners */
                           display: flex; /* Flexbox for internal alignment */
                           align-items: center; /* Vertically align icon and text */
                           margin-bottom: 1rem; /* Space between stacked cards */
                       }
               
                       /* Remove margin-bottom on the last feature card in the list */
                       .feature-list-container > div:last-child .feature-card {
                           margin-bottom: 0;
                       }
               
                       .feature-card:hover {
                           transform: translateY(-8px);
                           box-shadow: 0 15px 40px rgba(0, 43, 89, 0.15), 0 5px 15px rgba(0, 157, 230, 0.1);
                           border-color: rgba(0, 157, 230, 0.5) !important;
                       }
               
                       .feature-card:nth-child(1) { animation-delay: 0.1s; }
                       .feature-card:nth-child(2) { animation-delay: 0.2s; }
                       .feature-card:nth-child(3) { animation-delay: 0.3s; }
                       .feature-card:nth-child(4) { animation-delay: 0.4s; }
               
                       .feature-icon {
                           transition: all 0.3s ease;
                           box-shadow: 0 4px 12px rgba(0, 157, 230, 0.25);
                           width: 55px; /* Slightly larger icon container */
                           height: 55px;
                           min-width: 55px; /* Prevent shrinking */
                           min-height: 55px; /* Prevent shrinking */
                           border-radius: 0.5rem; /* Match card radius or slightly less */
                           margin-right: 1.25rem; /* More space between icon and text */
                       }
               
                       .feature-card:hover .feature-icon {
                           transform: scale(1.1);
                           box-shadow: 0 6px 18px rgba(0, 157, 230, 0.4);
                       }
               
                       .feature-card h5 {
                           color: #002B59;
                           font-size: 1.25rem; /* Slightly larger feature titles */
                           margin-bottom: 0.5rem;
                       }
               
                       .feature-card p {
                           color: #6c757d; /* Clearer muted text color */
                           font-size: 1rem;
                           line-height: 1.6;
                       }
               
                       /* Benefits Card */
                       .benefits-card {
                           position: relative;
                           overflow: hidden;
                           background: linear-gradient(135deg, #002B59 0%, #009DE6 100%);
                           color: white;
                           box-shadow: 0 15px 40px rgba(0, 43, 89, 0.3);
                           border: none;
                           padding: 2.5rem; /* More padding for a richer feel */
                           border-radius: 0.75rem; /* Match feature cards */
                           height: 100%; /* Ensure it stretches to match the height of feature cards */
                           display: flex;
                           flex-direction: column;
                           justify-content: center;
                       }
               
                       .benefits-card::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: 0;
                         right: 0;
                         bottom: 0;
                         background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
                         opacity: 0.5;
                         pointer-events: none;
                       }
               
                       .benefits-card h3 {
                           font-size: 1.8rem; /* Larger, more prominent heading */
                           margin-bottom: 1.5rem; /* More space below heading */
                       }
               
                       .benefits-list .fa-check-circle {
                           transition: transform 0.2s ease-out;
                           color: #00ff88;
                           font-size: 1.1rem; /* Slightly larger checkmark icon */
                           min-width: 20px; /* Ensure checkmark doesn't push text */
                       }
               
                       .benefits-list div:hover .fa-check-circle {
                           transform: scale(1.2);
                           color: #39ff14 !important;
                       }
               
                       .benefits-list span {
                           font-size: 1rem; /* Adjust benefit text size */
                           line-height: 1.6;
                           flex-grow: 1; /* Allow text to take available space */
                       }
               
                       /* General Button Hover Effect */
                       .btn:hover {
                         transform: translateY(-2px);
                         transition: transform 0.2s ease;
                       }
               
                       /* Responsive Adjustments */
                       @media (max-width: 991.98px) { /* For Bootstrap's lg breakpoint and smaller */
                           .features-section .row {
                               flex-direction: column; /* Stack columns on smaller screens */
                           }
               
                           .col-lg-8, .col-lg-4 {
                               width: 100%; /* Take full width */
                               max-width: 100%; /* Ensure it doesn't overflow */
                               margin-bottom: 2rem; /* Add space between stacked sections */
                           }
               
                           .benefits-card {
                               margin-bottom: 0; /* No extra margin when it's the last element */
                           }
               
                           .feature-card {
                               margin-bottom: 1.5rem; /* Increase space between feature cards when stacked */
                           }
                       }
               
                       @media (max-width: 767.98px) { /* For Bootstrap's md breakpoint and smaller */
                           .features-section {
                               padding-top: 3rem;
                               padding-bottom: 3rem;
                           }
               
                           .feature-card {
                               flex-direction: column; /* Stack icon and text vertically inside card */
                               text-align: center;
                               padding: 1.25rem;
                           }
               
                           .feature-icon {
                               margin-right: 0;
                               margin-bottom: 1rem; /* Space between stacked icon and text */
                           }
               
                           .feature-card h5 {
                               font-size: 1.15rem;
                           }
               
                           .feature-card p {
                               font-size: 0.9rem;
                           }
               
                           .benefits-card {
                               padding: 2rem;
                           }
               
                           .benefits-card h3 {
                               font-size: 1.5rem;
                           }
               
                           .benefits-list div {
                               align-items: flex-start; /* Ensure checkmark stays at the top of the text */
                           }
                       }
                     `}</style>
                     {/* Extraordinary Hero Section */}
                     <section
                       ref={heroRef}
                       className="position-relative overflow-hidden"
                       style={{
                         minHeight: '100vh',
                         display: 'flex',
                         alignItems: 'center',
                         position: 'relative'
                       }}
                     >
                       {/* Video Background */}
                       <div className="position-absolute w-100 h-100" style={{ zIndex: 0 }}>
                         <video
                           autoPlay
                           muted
                           loop
                           playsInline
                           className="w-100 h-100"
                           style={{
                             objectFit: 'cover',
                             objectPosition: 'center'
                           }}
                         >
                           <source src={homePageVideo} type="video/mp4" />
                           {/* Fallback for browsers that don't support video */}
                           Your browser does not support the video tag.
                         </video>
                       </div>

                       {/* Video Overlay for Text Readability */}
                       <div
                         className="position-absolute w-100 h-100"
                         style={{
                           zIndex: 1,
                           background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.85) 0%, rgba(0, 41, 86, 0.75) 50%, rgba(0, 41, 86, 0.85) 100%)',
                           backdropFilter: 'blur(2px)'
                         }}
                       />
               
                       <div className="container position-relative" style={{ zIndex: 10 }}>
                         <div className="row align-items-center min-vh-100 justify-content-center">
                           <div className="col-lg-8 col-xl-6 text-white text-center">
                             <div className="mb-4">
                               <h1
                                 ref={titleRef}
                                 className="display-1 fw-bold mb-4"
                                 style={{
                                   fontSize: 'clamp(3rem, 8vw, 4.4rem)',
                                   lineHeight: '1.1',
                                   color: '#ffffff',
                                   textShadow: '2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 41, 86, 0.6)',
                                   filter: 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))'
                                 }}
                               >
                                 Redefining.<br />
                                 <span style={{
                                   color: '#00a0e9',
                                   textShadow: '2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 160, 233, 0.8)'
                                 }}>Quality.</span>
                               </h1>
                             </div>
               
                             <div>
                               <p
                                 ref={subtitleRef}
                                 className="lead mb-5"
                                 style={{
                                   fontSize: '1.2rem',
                                   lineHeight: '1.6',
                                   color: '#ffffff',
                                   maxWidth: '600px',
                                   textShadow: '1px 1px 4px rgba(0, 0, 0, 0.8)',
                                   background: 'rgba(0, 41, 86, 0.3)',
                                   padding: '1.5rem',
                                   borderRadius: '12px',
                                   backdropFilter: 'blur(10px)',
                                   border: '1px solid rgba(255, 255, 255, 0.1)'
                                 }}
                               >
                                 Empowering businesses with cutting-edge technology solutions. 
                                 From IoT to AI, we transform your vision into reality with innovative 
                                 software that drives growth and success.
                               </p>
                             </div>
               

                           </div>
               

                         </div>
                       </div>
                     </section>
            </section>

            {/* Other sections of your page */}
            <ServicesSection />
            <ClientsSection />
            <QuickTestingSection />
        </>
    );
};

export default HomePage;